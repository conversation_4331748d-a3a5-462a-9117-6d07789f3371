#!/bin/bash

MODEL="qwen"
TOKENIZER="/models/Qwen3-30B-A3B/"
IGNORE_EOS="--ignore-eos"
DATASET_NAME="random"
PORT=8062
LOGFILE="${MODEL}_base.log"

# 文件锁，确保同一时间只有一个请求访问服务
LOCKFILE="/tmp/qwen3-30b_bench.lock"

acquire_lock() {
  exec 200>"$LOCKFILE"
  flock -n 200 \
    && return 0 \
    || { echo "已有其他进程在运行，退出..."; exit 1; }
}

release_lock() {
  flock -u 200
  rm -f "$LOCKFILE"
}

# 定义输入输出长度和并发数的组合，确保一一对应
COMBINATIONS=(
  "48 1000 500"
  "33 2000 500"
  "27 3600 500"
  # "8 20000 500"
)

# 1. 先启动服务
echo "启动Qwen3-30B-A3B服务..."
CUDA_VISIBLE_DEVICES=0,1 VLLM_USE_V1=0 \
  python -m vllm.entrypoints.openai.api_server \
  --model /models/Qwen3-30B-A3B/ \
  -tp 2 --dtype float16 --served-model-name qwen --enable-chunked-prefill --port $PORT --disable-log-requests  \
  > qwen3-30b_server.log 2>&1 &

SERVER_PID=$!

# 等待服务端口就绪
echo "等待服务端口 $PORT 就绪..."
# 使用curl替代nc，避免nc相关的not found打印
while ! curl -s --head http://localhost:$PORT/v1/models > /dev/null; do
  sleep 2
done
echo "服务已启动，开始发送请求..."

# 2. 依次发送请求，参数一一对应
for combination in "${COMBINATIONS[@]}"; do
  acquire_lock
  read -r VALUE RANDOM_INPUT_LEN RANDOM_OUTPUT_LEN <<< "$combination"
  echo "Running: num-prompts=$VALUE, max-concurrency=$VALUE, input-len=$RANDOM_INPUT_LEN, output-len=$RANDOM_OUTPUT_LEN" | tee -a $LOGFILE
  CUDA_VISIBLE_DEVICES_VISIBLE_DEVICES=0,1 python3 /home/<USER>/benchmarks/benchmark_serving.py \
    --model $MODEL \
    --tokenizer $TOKENIZER \
    --random-input-len $RANDOM_INPUT_LEN \
    --random-output-len $RANDOM_OUTPUT_LEN \
    --num-prompts $((VALUE * 10))\
    --max-concurrency $VALUE \
    $IGNORE_EOS \
    --dataset-name $DATASET_NAME \
    --port $PORT \
    >> $LOGFILE 2>&1
  echo "---------------------------------------------" | tee -a $LOGFILE
  release_lock
done

# 3. 结束后关闭服务
echo "测试完成，关闭服务..."
kill $SERVER_PID
