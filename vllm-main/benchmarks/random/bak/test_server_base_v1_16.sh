#!/bin/bash
MODEL="mixtral"
TOKENIZER="/models/Qwen2.5-7B-Instruct/Qwen/Qwen2.5-7B-Instruct "
IGNORE_EOS="--ignore-eos"
DATASET_NAME="random"
PORT=8002
LOGFILE="${MODEL}_base_v1_16.log"
# 定义输入输出长度的组合
COMBINATIONS=(
  "1 128"
  "1024 128"
  "512 128"
  "4096 1"
)

VALUES=(1 4 8 16 32 )
for value in "${VALUES[@]}"; do
  for combination in "${COMBINATIONS[@]}"; do
    RANDOM_INPUT_LEN=$(echo $combination | awk '{print $1}')
    RANDOM_OUTPUT_LEN=$(echo $combination | awk '{print $2}')
    echo "Running: num-prompts=$value, max-concurrency=$value, input-len=$RANDOM_INPUT_LEN, output-len=$RANDOM_OUTPUT_LEN" | tee -a $LOGFILE
    CUDA_VISIBLE_DEVICES=7 python3 /home/<USER>/benchmarks/benchmark_serving.py \
      --model $MODEL \
      --tokenizer $TOKENIZER \
      --random-input-len $RANDOM_INPUT_LEN \
      --random-output-len $RANDOM_OUTPUT_LEN \
      --num-prompts $value \
      --max-concurrency $value \
      $IGNORE_EOS \
      --dataset-name $DATASET_NAME \
      --port $PORT \
      >> $LOGFILE 2>&1
    echo "---------------------------------------------" | tee -a $LOGFILE
  done
done

# VLLM_USE_V1=0 \
# CUDA_VISIBLE_DEVICES=1 \
# python -m vllm.entrypoints.openai.api_server \
#   --model /models/qwen2.5/Qwen2.5-7B-Instruct/ \
#   -tp 1 \
#   --dtype float16 \
#   --served-model-name mixtral \
#   --enforce-eager \
#   --port 8001