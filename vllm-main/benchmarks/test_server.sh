#!/bin/bash


MODEL="mixtral"
TOKENIZER="/models/Mixtral-8x22B-Instruct-v0.1/"
IGNORE_EOS="--ignore-eos"
DATASET_NAME="random"
PORT=8000
LOGFILE="${MODEL}.log"

# 定义输入输出长度的组合
COMBINATIONS=(
  "1 128"
  "1024 512"
  "512 1024"
  "4096 1"
)

VALUES=(600)
for value in "${VALUES[@]}"; do
  for combination in "${COMBINATIONS[@]}"; do
    RANDOM_INPUT_LEN=$(echo $combination | awk '{print $1}')
    RANDOM_OUTPUT_LEN=$(echo $combination | awk '{print $2}')
    echo "Running: num-prompts=$value, max-concurrency=$value, input-len=$RANDOM_INPUT_LEN, output-len=$RANDOM_OUTPUT_LEN" | tee -a $LOGFILE
    CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 python3 benchmark_serving.py \
      --model $MODEL \
      --tokenizer $TOKENIZER \
      --random-input-len $RANDOM_INPUT_LEN \
      --random-output-len $RANDOM_OUTPUT_LEN \
      --num-prompts $value \
      --max-concurrency $value \
      $IGNORE_EOS \
      --dataset-name $DATASET_NAME \
      --port $PORT \
      >> $LOGFILE 2>&1
    echo "---------------------------------------------" | tee -a $LOGFILE
  done
done