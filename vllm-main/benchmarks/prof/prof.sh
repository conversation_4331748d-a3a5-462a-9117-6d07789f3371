#!/bin/bash

# 文件锁机制，防止脚本重复运行
LOCKFILE="/tmp/prof_llama_test.lock"
if [ -f "$LOCKFILE" ]; then
    echo "脚本已在运行中 (锁文件: $LOCKFILE)，退出..."
    exit 1
fi
echo $$ > "$LOCKFILE"

# 清理函数
cleanup() {
    echo "清理锁文件..."
    rm -f "$LOCKFILE"
    exit
}
trap cleanup EXIT INT TERM

# 定义TP数
QWEN_TP=2
LLAMA_TP=4

# 启动qwen服务（如不需要可注释）
nohup bash -c 'CUDA_VISIBLE_DEVICES=0,1  VLLM_USE_V1=0 VLLM_TORCH_PROFILER_DIR=/home/<USER>/benchmarks/prof/qwen3-30B/ python -m vllm.entrypoints.openai.api_server --model /models/Qwen3-30B-A3B/ -tp 2 --dtype float16 --served-model-name qwen  --port 8061 --disable-log-requests --no-enable-chunked-prefill  ' >/home/<USER>/benchmarks/prof/Qwen3-30B-A3B_server.log 2>&1 &
SERVER1_PID=$!

# 启动llama服务
nohup bash -c 'CUDA_VISIBLE_DEVICES=2,3,4,7  VLLM_USE_V1=0 VLLM_TORCH_PROFILER_DIR=/home/<USER>/benchmarks/prof/llama3.3-70B/ python -m vllm.entrypoints.openai.api_server --model /models/local_model/Llama-3.3-70B-Instruct -tp 4 --dtype float16 --served-model-name llama  --port 8062 --disable-log-requests --no-enable-chunked-prefill ' > /home/<USER>/benchmarks/prof/Llama-33-70B-Instruct_server.log 2>&1 &
SERVER2_PID=$!

while ! curl -s --head http://localhost:8061/v1/models > /dev/null; do sleep 2; done
while ! curl -s --head http://localhost:8062/v1/models > /dev/null; do sleep 2; done

# --- Test Cases ---
QWEN_CASES=(
   "1000 54 2000"
   "200 67 4000"
   "1000 114 2000"
   "200 120 4000"
)

LLAMA_CASES=(
   "1000 27 2000"
   "200 32 4000"
   "1000 55 2000"
   "200 65 4000"
)

# 定义每个模型的请求锁文件
QWEN_REQ_LOCK="/tmp/prof_qwen_req.lock"
LLAMA_REQ_LOCK="/tmp/prof_llama_req.lock"

(
    PROF_DIR="/home/<USER>/benchmarks/prof/qwen3-30B"
    LOG_FILE="/home/<USER>/benchmarks/prof/Qwen3-30B-A3B.log"
   
    for case in "${QWEN_CASES[@]}"; do
        read -r input_len num_prompts random_output_len <<<"$case"

        # 获取qwen请求锁，保证同一时刻只有一个请求
        exec 9>"$QWEN_REQ_LOCK"
        flock 9

        echo "${num_prompts}-${input_len}-${random_output_len}" >> "${LOG_FILE}"

        CUDA_VISIBLE_DEVICES=0,1 python3 /home/<USER>/benchmarks/benchmark_serving.py \
            --model qwen \
            --tokenizer /models/Qwen3-30B-A3B/ \
            --random-input-len "${input_len}" \
            --random-output-len "${random_output_len}" \
            --num-prompts "${num_prompts}" \
            --max-concurrency "${num_prompts}" \
            --ignore-eos \
            --dataset-name random \
            --port 8061 \
            --profile \
            >> "${LOG_FILE}" 2>&1

        # 释放qwen请求锁
        flock -u 9
        exec 9>&-

        sleep 10;
        OUT_DIR="${PROF_DIR}/${num_prompts}_${input_len}_${random_output_len}"
        while true; do
            count=$(ls ${PROF_DIR}/*.gz 2>/dev/null | wc -l)
            if [ "$count" -ge $QWEN_TP ]; then
                break
            fi
            sleep 2
        done

        mkdir -p "${OUT_DIR}"
        mv "${PROF_DIR}"/*.gz "${OUT_DIR}"/
    done
) &
TEST1_PID=$!

(
    PROF_DIR="/home/<USER>/benchmarks/prof/llama3.3-70B"
    LOG_FILE="/home/<USER>/benchmarks/prof/Llama-33-70B-Instruct.log"

    for case in "${LLAMA_CASES[@]}"; do
        read -r input_len num_prompts random_output_len <<<"$case"

        # 获取llama请求锁，保证同一时刻只有一个请求
        exec 8>"$LLAMA_REQ_LOCK"
        flock 8

        echo "${num_prompts}-${input_len}-${random_output_len}" >> "${LOG_FILE}"

        CUDA_VISIBLE_DEVICES=2,3,4,7 python3 /home/<USER>/benchmarks/benchmark_serving.py \
            --model llama \
            --tokenizer /models/local_model/Llama-3.3-70B-Instruct \
            --random-input-len "${input_len}" \
            --random-output-len "${random_output_len}" \
            --num-prompts "${num_prompts}" \
            --max-concurrency "${num_prompts}" \
            --ignore-eos \
            --dataset-name random \
            --port 8062 \
            --profile \
            >> "${LOG_FILE}" 2>&1

        # 释放llama请求锁
        flock -u 8
        exec 8>&-

        OUT_DIR="${PROF_DIR}/${num_prompts}_${input_len}_${random_output_len}"
        while true; do
            count=$(find "${PROF_DIR}" -maxdepth 1 -name '*.gz' | wc -l)
            if [ "$count" -ge $LLAMA_TP ]; then
                break
            fi
            sleep 2
        done
        sleep 60;
        mkdir -p "${OUT_DIR}"
        found_gz=$(find "${PROF_DIR}" -maxdepth 1 -name '*.gz' | wc -l)
        if [ "$found_gz" -gt 0 ]; then
            find "${PROF_DIR}" -maxdepth 1 -name '*.gz' -print0 | xargs -0 mv -t "${OUT_DIR}"/
        else
            echo "警告：mv时未找到任何 .gz 文件，可能是被其他进程提前移动或并发问题导致" >> "${LOG_FILE}"
        fi
    done
) &
TEST2_PID=$!

wait $TEST1_PID
wait $TEST2_PID

kill $SERVER1_PID
kill $SERVER2_PID