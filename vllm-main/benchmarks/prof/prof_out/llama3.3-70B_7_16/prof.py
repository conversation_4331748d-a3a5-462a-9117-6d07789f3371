import json
import pandas as pd
import os
import glob
import re
from collections import OrderedDict

def process_xlse(file):
    # 加载 JSON 数据
    file_path=file
    with open(file, "r") as f:
        data = json.load(f)  # 假设 data 是一个列表，包含所有记录
    # 新增：对traceEvents按ts排序，保证分界点准确
    data['traceEvents'].sort(key=lambda x: x.get('ts', 0))
    file_name = os.path.basename(file_path)  # 提取文件名（含扩展名）
    print(file_name)  # 输出: 1.json

    # 如果想去掉扩展名，只保留 '1':
    name_without_ext = os.path.splitext(file_name)[0]
    
    lay_key_words = [
                # bw prefill kernel 
                "fused_add_rms_kernel_opt",
                "fused_add_rms_kernel",
                "Cijk_",
                "rotary_embedding_kernel",
                "reshape_and_cache_kernel_cuda",
                "flash_fwd_kernel",
                "ncclKernel_AllReduce_Ring_Simple_Sum",
                "act_and_mul_kernel",

                "elementwise_kernel",
                "native::reduce_kernel",
                "moe::topkGatingSoftmax",
                "moe::moe_align_block_size_global_mem_kernel",
                "fused_moe_kernel",
                "moe::moe_sum_kernel",
                "moe::moe_align_block_size_kernel",
                "paged_attention_streamk_kernel",

                
                #bw decode kernel
                "paged_attention_kernel",
                "paged_attention_reduce_kernel",
                "cross_device_reduce_",

                #nv prefill kernel
                "fused_add_rms_norm_kernel",
                "ampere_bf16_s16816gem",
                "ampere_fp16_s16816gem",
                "reshape_and_cache_flash_kernel",
                "flash::flash_fwd_kernel",
                "ncclDevKernel_AllReduce_Sum",

                #nv decode kernel
                "flash_fwd_splitkv_combine_kernel",
                "flash_fwd_splitkv_kernel",
                "cublasLt::splitKreduce_kernel",
                "cutlass::Kernel2",

                #quant kernel        
                "matmul_kernel",
                "per_token_quant_int8",
              

                "_w8a8_block_int8",

                #"cunn_SoftMaxForward",
              
            
             
                "dynamic_scaled_int8",
             
             
            ]
    combined_words = "|".join(lay_key_words)

    # 第一步：找到第一次paged_attention出现的时间点作为分割点
    transition_time = None
    for entry in data['traceEvents']:
        if 'paged_attention' in entry.get('name', '') or 'flash::flash_fwd_splitkv_kernel' in entry.get('name', ''):
            print(f"在第 {data['traceEvents'].index(entry)} 个 entry 处检测到: {entry.get('name', '')}")
            transition_time = entry.get('ts', 0)
            print(f"检测到第一次paged_attention，时间戳: {transition_time}")
            break
    
    if transition_time is None:
        print("未检测到paged_attention，将使用时间中位数作为分割点")

    matched_key_words=set()
    prefill_dict={}
    decode_dict={}
    
    #统计所有kernel耗时
    total_time=0
    prefill_total_time=0  # 匹配kernel的时间
    decode_total_time=0   # 匹配kernel的时间
    
    # 统计各阶段的真实总时间
    prefill_phase_total_time=0
    decode_phase_total_time=0
         
    found_any = False  # 新增：用于标记是否找到匹配
    for i, entry in enumerate(data['traceEvents']):
        #将name与感兴趣的字段进行匹配
        matches = re.findall(combined_words, entry["name"])
        if entry.get('name') and (entry.get('cat')=="kernel" or entry.get('cat')=="gpu_memcpy") and entry.get('args'):
            if entry["args"].get('stream') is not None:  #==0  
                duration = float(entry["dur"])
                current_time = entry.get('ts', 0)
                total_time += duration

                # 判断是prefill还是decode阶段
                if transition_time is None:
                    # 如果没找到paged_attention，使用时间中位数分割
                    # 这里简单用事件序号的一半作为分割点
                    is_prefill = i < len(data['traceEvents']) // 2
                else:
                    is_prefill = current_time < transition_time

                # 统计各阶段的真实总时间
                if is_prefill:
                    prefill_phase_total_time += duration
                else:
                    decode_phase_total_time += duration

                if matches:
                    found_any = True  # 标记找到
                    key = matches[0]

                    if is_prefill:
                        # prefill阶段
                        prefill_total_time += duration
                        if key in prefill_dict:
                            prefill_dict[key]["cost_time"] += duration
                            prefill_dict[key]["call_time"] += 1
                        else:
                            prefill_dict[key] = {"cost_time": duration, "call_time": 1}
                    else:
                        # decode阶段
                        decode_total_time += duration
                        if key in decode_dict:
                            decode_dict[key]["cost_time"] += duration
                            decode_dict[key]["call_time"] += 1
                        else:
                            decode_dict[key] = {"cost_time": duration, "call_time": 1}
    if not found_any:
        raise RuntimeError("未找到任何匹配的kernel，请检查lay_key_words或traceEvents内容。")

    # 处理prefill阶段数据
    def process_phase_data(phase_dict, phase_name, phase_total_time, phase_real_total_time):
        if not phase_dict:
            print(f"{phase_name}阶段未检测到匹配的kernel")
            return None
            
        #将字典里的元素cost time取round
        for key,value in phase_dict.items():
            value["cost_time"] =round(value["cost_time"] ,3)
            value["percent"]=round(value["cost_time"]/total_time,4) if total_time > 0 else 0
            value["phase_percent"]=round(value["cost_time"]/phase_real_total_time,4) if phase_real_total_time > 0 else 0
            # 新增：算子耗时
            value["avg_time"] = round(value["cost_time"]/value["call_time"], 3) if value["call_time"] > 0 else 0
            
        print(f"\n=== {phase_name}阶段统计 ===")
        print(f"{phase_name}阶段总时间: {phase_real_total_time/1000:.2f}ms ({phase_real_total_time/total_time*100:.1f}%)")
        print(f"{phase_name}阶段匹配kernel时间: {phase_total_time/1000:.2f}ms ({phase_total_time/total_time*100:.1f}%)")
        
        #按照percent 高低进行排序
        sorted_items = sorted(phase_dict.items(), key=lambda x: x[1]['percent'], reverse=True)

        # 如果你想要一个有序字典（Python 3.7+中字典保持插入顺序）
        sorted_dict = OrderedDict(sorted_items)

        # 打印排序后的结果
        for key, value in sorted_items[:10]:  # 只显示前10个
            print(f"{key}: {value}")

        #生成DataFrame并导出Excel              
        df = pd.DataFrame.from_dict(
            {k: {
                '总耗时 (μs)': v['cost_time'],
                '调用次数': v['call_time'],
                '单算子耗时(us)': v['avg_time'],
                f'{phase_name}内占比': f"{v['phase_percent']*100:.2f}%"
            } for k, v in sorted_dict.items()}, 
            orient='index'
            ).reset_index()

        # 重命名列
        df.columns = ['Kernel 名称', '总耗时 (μs)', '调用次数', '单算子耗时(us)', f'{phase_name}内占比']

        # 导出为 Excel
        os.makedirs("./result/", exist_ok=True)
        output_file = f"./result/{name_without_ext}_{phase_name.lower()}.xlsx"
        df.to_excel(output_file, index=False, engine='openpyxl')

        print(f"{phase_name} Excel 文件已生成: {output_file}")
        return sorted_dict
    
    # 处理两个阶段的数据
    prefill_result = process_phase_data(prefill_dict, "Prefill", prefill_total_time, prefill_phase_total_time)
    decode_result = process_phase_data(decode_dict, "Decode", decode_total_time, decode_phase_total_time)
    
    # 生成对比分析
    print(f"\n=== 整体分析 ===")
    print(f"总耗时: {total_time/1000:.2f}ms")
    print(f"Prefill阶段真实占比: {prefill_phase_total_time/total_time*100:.1f}%")
    print(f"Decode阶段真实占比: {decode_phase_total_time/total_time*100:.1f}%")  
    print(f"Prefill匹配kernel占比: {prefill_total_time/total_time*100:.1f}%")  
    print(f"Decode匹配kernel占比: {decode_total_time/total_time*100:.1f}%")
    
    if transition_time:
        print(f"阶段分割点: timestamp {transition_time}")
    else:
        print("阶段分割点: 使用中位数估算")

def main():
    folder_path="/home/<USER>/benchmarks/prof/prof_out/llama3.3-70B_7_16/"
    # 获取所有 .json 文件的完整路径
    json_files = glob.glob(f"{folder_path}/*.json")
    # 如果只需要文件名（不含路径）
    json_file_names = [os.path.basename(file) for file in json_files]
    print(json_file_names)  # 输出所有 .json 文件名列表
    for it in json_file_names:

        process_xlse(folder_path+it)

if __name__ == "__main__":
    main()