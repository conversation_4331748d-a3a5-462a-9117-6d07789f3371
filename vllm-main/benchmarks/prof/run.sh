#!/bin/bash

# 需要监控的GPU编号
GPUS=(2 3 4 5 6 7)
# GPU-Util阈值
UTIL_THRESHOLD=5
# 检查间隔（秒）
CHECK_INTERVAL=10
# 满足条件的累计时长（秒）
REQUIRED_DURATION=60

# 检查函数：返回0表示全部空卡且util低于阈值，否则返回1
check_gpus_free() {
    # 获取nvidia-smi输出
    smi_out=$(nvidia-smi --query-gpu=index,utilization.gpu,memory.used --format=csv,noheader,nounits)
    # 获取进程信息
    proc_out=$(nvidia-smi pmon -c 1)

    for gpu in "${GPUS[@]}"; do
        # 检查util
        util=$(echo "$smi_out" | awk -F',' -v g="$gpu" '$1==g {print $2}' | tr -d ' ')
        if [[ -z "$util" || "$util" -ge $UTIL_THRESHOLD ]]; then
            return 1
        fi
        # 检查进程
        # nvidia-smi pmon输出格式: # gpu pid type sm mem enc dec command
        # 跳过表头，查找该GPU是否有进程
        has_proc=$(echo "$proc_out" | awk -v g="$gpu" 'NR>2 && $1==g && $2!="-"){print $0}' | wc -l)
        if [[ "$has_proc" -gt 0 ]]; then
            return 1
        fi
    done
    return 0
}

echo "开始监控GPU ${GPUS[*]}，当全部空卡且GPU-Util<$UTIL_THRESHOLD 持续${REQUIRED_DURATION}秒后，启动prof.sh"

accum=0
while true; do
    if check_gpus_free; then
        accum=$((accum + CHECK_INTERVAL))
        echo "$(date): 满足条件，已累计 $accum 秒"
        if [[ $accum -ge $REQUIRED_DURATION ]]; then
            echo "$(date): 条件满足，启动 /home/<USER>/benchmarks/prof/prof.sh"
            bash /home/<USER>/benchmarks/prof/prof.sh
            break
        fi
    else
        # 不满足条件，重置计时
        if [[ $accum -ne 0 ]]; then
            echo "$(date): 条件中断，计时重置"
        fi
        accum=0
    fi
    sleep $CHECK_INTERVAL
done

# 调用MCP反馈
python3 -m mcp_feedback_enhanced --message "prof.sh已自动启动。如有建议或需求请反馈。"
