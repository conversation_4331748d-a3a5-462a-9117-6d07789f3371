import re
import csv

log_file = "mixtral.log"
csv_file = "result.csv"

# 结果存储
results = []

with open(log_file, "r", encoding="utf-8") as f:
    lines = f.readlines()

# 正则表达式
run_re = re.compile(r"Running: num-prompts=(\d+), max-concurrency=\d+, input-len=(\d+), output-len=(\d+)")
total_token_re = re.compile(r"Total Token throughput \(tok/s\):\s+([\d\.]+)")
output_token_re = re.compile(r"Output token throughput \(tok/s\):\s+([\d\.]+)")
mean_ttft_re = re.compile(r"Mean TTFT \(ms\):\s+([\d\.]+)")
mean_tpot_re = re.compile(r"Mean TPOT \(ms\):\s+([\d\.]+)")

i = 0
while i < len(lines):
    m = run_re.search(lines[i])
    if m:
        num_prompts, input_len, output_len = m.groups()
        # 初始化
        total_token = output_token = mean_ttft = mean_tpot = ""
        # 向下查找对应的指标
        for j in range(i, min(i+1000, len(lines))):  # 只在后面100行内查找
            if total_token == "":
                m1 = total_token_re.search(lines[j])
                if m1:
                    total_token = m1.group(1)
            if output_token == "":
                m2 = output_token_re.search(lines[j])
                if m2:
                    output_token = m2.group(1)
            if mean_ttft == "":
                m3 = mean_ttft_re.search(lines[j])
                if m3:
                    mean_ttft = m3.group(1)
            if mean_tpot == "":
                m4 = mean_tpot_re.search(lines[j])
                if m4:
                    mean_tpot = m4.group(1)
            # 如果都找到了就跳出
            if all([total_token, output_token, mean_ttft, mean_tpot]):
                break
        results.append([num_prompts, input_len, output_len, total_token, output_token, mean_ttft, mean_tpot])
    i += 1

# 写入CSV文件
with open(csv_file, "w", newline="") as f:
    writer = csv.writer(f)
    writer.writerow([
        "num-prompts", "input-len", "output-len",
        "Total Token throughput (tok/s):",
        "Output token throughput (tok/s):",
        "Mean TTFT (ms):", "Mean TPOT (ms):"
    ])
    writer.writerows(results)

print(f"已写入 {csv_file}")