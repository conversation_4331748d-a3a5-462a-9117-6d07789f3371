rm -rf ~/.cache
model_path=/mnt/data/model/Mixtral-8x22B-Instruct-v0.1
model=${model_path##*/}
time=$(date "+%m%d-%H:%M")
mode="server"
log_dir="/home/<USER>/vllm/benchmarks/test/server/${model}/${mode}/${time}"
port=8082
if [ ! -f ${log_dir} ]; then
    mkdir ${log_dir} -p
fi

# export FA_PAD=0
# export GEMM_PAD=0
# export HSA_FORCE_FINE_GRAIN_PCIE=1
export NCCL_MAX_NCHANNELS=16
export NCCL_MIN_NCHANNELS=16
# export NCCL_P2P_LEVEL=SYS
# export NCCL_LAUNCH_MODE=GROUP
# export ROCBLAS_COMPUTETYPE_FP16R=0
export VLLM_ZERO_OVERHEAD=1
export VLLM_USE_V1=0

cd /home/<USER>/test/kunlun-benchmark
HIP_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 ./kunlun-benchmark vllm server --prompt_type normal_distribution --max_input_len 1000 --min_input_len 800 --max_output_len 500 --min_output_len 300 --concurrency 145 --query_num 1450 --port ${port} --tp 8 --model_path ${model_path} --work_mode manual --result_dir /mnt/result 2>&1 | tee ${log_dir}/moe22b_1k_server_1.log

HIP_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 ./kunlun-benchmark vllm server --prompt_type normal_distribution --max_input_len 2000 --min_input_len 1600 --max_output_len 500 --min_output_len 300 --concurrency 90 --query_num 900 --port ${port} --tp 8 --model_path ${model_path} --work_mode manual --result_dir /mnt/result 2>&1 | tee ${log_dir}/moe22b_2k_server_1.log

HIP_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 ./kunlun-benchmark vllm server --prompt_type normal_distribution --max_input_len 3600 --min_input_len 3000 --max_output_len 500 --min_output_len 300 --concurrency 60 --query_num 600 --port ${port} --tp 8 --model_path ${model_path} --work_mode manual --result_dir /mnt/result 2>&1 | tee ${log_dir}/moe22b_3k_server_1.log
