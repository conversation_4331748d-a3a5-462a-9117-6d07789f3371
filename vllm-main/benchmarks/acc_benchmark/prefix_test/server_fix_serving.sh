#!/bin/bash

MODEL="qwen"
TOKENIZER="/models/Qwen3-30B-A3B/"
IGNORE_EOS="--ignore-eos"
DATASET_NAME="prefix_cache"
PORT=8061
REQUEST_LOGFILE="${MODEL}_fix_serving_request.log"
SERVER_LOGFILE="${MODEL}_fix_serving_server.log"

# 文件锁，确保同一时间只有一个请求访问服务
LOCKFILE="/tmp/qwen3-30b_bench-fix_base.lock"

acquire_lock() {
  exec 200>"$LOCKFILE"
  flock -n 200 \
    && return 0 \
    || { echo "已有其他进程在运行，退出..."; exit 1; }
}

release_lock() {
  flock -u 200
  rm -f "$LOCKFILE"
}

# 定义输入输出长度和并发数的组合，确保一一对应
# 输入长度范围：0.8~1k, 1.6~2k, 3.0~3.6k, 16~20k
# 输出长度范围：0.3~0.5k
# 这里的输入输出长度均为token数，按范围中间值或上下界取整
COMBINATIONS=(
  "1 4096 1 0" 
  "1 4096 1 0.5"
  "1 4096 1 1"
  "32 4096 1 0" 
  "32 4096 1 0.5"
  "32 4096 1 1"
  "64 4096 1 0"  
  "64 4096 1 0.5"
  "64 4096 1 1"
  "128 4096 1 0"   
  "128 4096 1 0.5"
  "128 4096 1 1"

)

# 1. 先启动服务
echo "启动Qwen3-30B-A3B服务..."
CUDA_VISIBLE_DEVICES=2,3 VLLM_SERVER_DEV_MODE=1 VLLM_USE_V1=0\
  python -m vllm.entrypoints.openai.api_server \
  --model $TOKENIZER \
  -tp 2 --dtype float16 --served-model-name qwen  --port $PORT --disable-log-requests --no-enable-chunked-prefill --enable-prefix-caching \
  > $SERVER_LOGFILE 2>&1 &

SERVER_PID=$!

# 等待服务端口就绪
echo "等待服务端口 $PORT 就绪..."
# 使用curl替代nc，避免nc相关的not found打印
while ! curl -s --head http://localhost:$PORT/v1/models > /dev/null; do
  sleep 2
done
echo "服务已启动，开始发送请求..."

# 2. 依次发送请求，参数一一对应
for combination in "${COMBINATIONS[@]}"; do
  acquire_lock
  read -r VALUE RANDOM_INPUT_LEN RANDOM_OUTPUT_LEN TARGET_HIT_RATE <<< "$combination"
  echo "Running: num-prompts=$VALUE, max-concurrency=$VALUE, input-len=$RANDOM_INPUT_LEN, output-len=$RANDOM_OUTPUT_LEN, target-hit-rate=$TARGET_HIT_RATE" | tee -a $LOGFILE
  CUDA_VISIBLE_DEVICES=2,3 python3 /home/<USER>/benchmarks/acc_benchmark/prefix_test/benchmark_prefix_caching.py \
    --model $MODEL \
    --tokenizer $TOKENIZER \
    --random-input-len $RANDOM_INPUT_LEN \
    --random-output-len $RANDOM_OUTPUT_LEN \
    --num-prompts $VALUE\
    --max-concurrency $VALUE \
    --target-hit-rate $TARGET_HIT_RATE \
    $IGNORE_EOS \
    --dataset-name $DATASET_NAME \
    --port $PORT \
    >> $REQUEST_LOGFILE 2>&1
  echo "---------------------------------------------" | tee -a $LOGFILE
  release_lock
done

# 3. 结束后关闭服务
echo "测试完成，关闭服务..."
kill $SERVER_PID
