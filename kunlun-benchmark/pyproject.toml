[tool.poetry]
name = "benchmark"
version = "0.1.0"
description = ""
authors = ["xinnan.hou <<EMAIL>>"]
readme = "README.md"

[[tool.poetry.source]]
name = "tuna"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
priority = "primary"

[tool.poetry.dependencies]
python = "^3.8.1"
#requests = "^2.28.1"
#pyyaml = "*"
#
#prettytable = "*"
#pytz = "*"
#transformers = "*"
#pydantic = "^2.0.0"
#datasets = "*"
#
#click = "*"
#loguru = "*"
#oss2 = "*"
loguru = "^0.7.3"
oss2 = "^2.19.1"
jsonlines = "^4.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
# use flake8's line-length
line-length = 120
target-version = ['py37']
skip-string-normalization = true

[tool.poetry.scripts]
lint = "scripts.scripts:format_with_black"

[virtual_envs]
create = false
