absl-py==2.1.0
aiohappyeyeballs @ file:///rapids/aiohappyeyeballs-2.4.0-py3-none-any.whl#sha256=7ce92076e249169a13c2f49320d1967425eaf1f407522d707d59cac7628d62bd
aiohttp @ file:///rapids/aiohttp-3.10.5-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=0912b8a8fadeb32ff67a3ed44249448c20148397c1ed905d5dac185b4ca547bb
aiosignal @ file:///rapids/aiosignal-1.3.1-py3-none-any.whl#sha256=f8376fb07dd1e86a584e4fcdec80b36b7f81aac666ebc724e2c090300dd83b17
airportsdata==20250224
annotated-types==0.7.0
anyio==4.6.0
apex @ file:///opt/pytorch/apex
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asciitree @ file:///rapids/asciitree-0.3.3-py3-none-any.whl#sha256=73c95820eee9cc681353c4d9a0b246e20716f4e29256956eb95f345d2b389318
astor==0.8.1
asttokens==2.4.1
astunparse==1.6.3
async-lru==2.0.4
async-timeout @ file:///rapids/async_timeout-4.0.3-py3-none-any.whl#sha256=7405140ff1230c310e51dc27b3145b9092d659ce68ff733fb0cefe3ee42be028
attrs==24.2.0
audioread==3.0.1
babel==2.16.0
backports.tarfile==1.2.0
beautifulsoup4==4.12.3
black==24.8.0
blake3==1.0.4
bleach==6.1.0
blis==0.7.11
build==1.2.2.post1
CacheControl==0.14.3
cachetools @ file:///rapids/cachetools-5.5.0-py3-none-any.whl#sha256=02134e8439cdc2ffb62023ce1debca2944c3f289d66bb17ead3ab3dede74b292
catalogue==2.0.10
certifi==2024.8.30
cffi @ file:///rapids/cffi-1.17.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=24aa705a5f5bd3a8bcfa4d123f03413de5d86e497435693b638cbffb7d5d8a1b
charset-normalizer==3.3.2
cleo==2.1.0
click==8.1.7
cloudpathlib==0.19.0
cloudpickle @ file:///rapids/cloudpickle-3.0.0-py3-none-any.whl#sha256=246ee7d0c295602a036e86369c77fecda4ab17b506496730f2f576d9016fd9c7
cmake==3.30.4
comm==0.2.2
compressed-tensors==0.9.3
confection==0.1.5
contourpy==1.3.0
crashtest==0.4.1
cryptography==45.0.3
cuda-python @ file:///rapids/cuda_python-12.6.0-cp310-cp310-linux_x86_64.whl#sha256=baa9394932110482bb6c1323f4c3d3f16ce9c7717de93884fd600f40fb717449
cudf @ file:///rapids/cudf-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=daf662f89e0ace9bd55ebf38f8a21410977b92f0777c64a10c6147ac625e58db
cudf-polars @ file:///rapids/cudf_polars-24.8.0-py3-none-any.whl#sha256=00c4c717684472322fe15e9dce6948861f62f1c2d6d1bc8a3cf5b628d82ed0b5
cugraph @ file:///rapids/cugraph-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=4316e56793e80292aff28a3a3fb53cc842bc530c7776f774c0a7ec062688dffb
cugraph-dgl @ file:///rapids/cugraph_dgl-24.8.0-py3-none-any.whl#sha256=41ed7650d4e7bf7307db40527ae1f9d7753fc99e05f7d1c564bd81b8c5a8d818
cugraph-equivariant @ file:///rapids/cugraph_equivariant-24.8.0-py3-none-any.whl#sha256=539ed60d640508087874d683d67670ec90614cf323d4208a399b8d08c9b7a0fb
cugraph-pyg @ file:///rapids/cugraph_pyg-24.8.0-py3-none-any.whl#sha256=7be11146859d3074a9cecb216196a46101ce8b0d5863c6db8b3eefc3263028c2
cugraph-service-client @ file:///rapids/cugraph_service_client-24.8.0-py3-none-any.whl#sha256=a7b75be3f71249828e0ab562f65ef06143d2ffe771ee5be35e270cd6ebc8f465
cugraph-service-server @ file:///rapids/cugraph_service_server-24.8.0-py3-none-any.whl#sha256=31a78999b49406aeded7d208ee4aa2f3476b696910d143772237394fb7b6a65a
cuml @ file:///rapids/cuml-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=052de4f68c2b9c1da1f927d67b0a0a76bab3f2444e1e31711e94e912d8d6bfea
cupy-cuda12x @ file:///rapids/cupy_cuda12x-13.2.0-cp310-cp310-linux_x86_64.whl#sha256=fd432350fc0da3168eeb9e6a3b611ff11862135d86dfc1f2157a5b95e86753bf
cycler==0.12.1
cymem==2.0.8
Cython==3.0.11
dask @ file:///rapids/dask-2024.7.1-py3-none-any.whl#sha256=dd046840050376c317de90629db5c6197adda820176cf3e2df10c3219d11951f
dask-cuda @ file:///rapids/dask_cuda-24.8.0-py3-none-any.whl#sha256=55bc47ff528cdb6b10c08ec20a76d423eaffb9a65b4bc6a0159927e3c806db34
dask-cudf @ file:///rapids/dask_cudf-24.8.0-py3-none-any.whl#sha256=80b69b35ecee6879994455d014fdfe7d4604b072ee7b2008d60a38d941fc06fb
dask-expr @ file:///rapids/dask_expr-1.1.9-py3-none-any.whl#sha256=6a73d2ef8a4b697db476163ebfc661f7f4b6e12c4e483e8bf207e4d00e574f0c
datasets==3.6.0
debugpy==1.8.6
decorator==5.1.1
defusedxml==0.7.1
Deprecated==1.2.18
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distributed @ file:///rapids/distributed-2024.7.1-py3-none-any.whl#sha256=d5ac38d9682c191e6582c86ebf37c10d7adb60bf4a95048a05ae4fb0866119bc
distributed-ucxx @ file:///rapids/distributed_ucxx-0.39.0-py3-none-any.whl#sha256=429d2039aa063b899621e9ddb7d392e13da9beea59d617f1283d850f46f46f25
distro==1.9.0
dm-tree==0.1.8
dnspython==2.7.0
dulwich==0.22.8
einops==0.8.0
email_validator==2.2.0
entrypoints @ file:///rapids/entrypoints-0.4-py3-none-any.whl#sha256=f174b5ff827504fd3cd97cc3f8649f3693f51538c7e4bdf3ef002c8429d42f9f
exceptiongroup==1.2.2
execnet==2.1.1
executing==2.1.0
expecttest==0.1.3
fastapi==0.115.12
fastapi-cli==0.0.7
fasteners @ file:///rapids/fasteners-0.19-py3-none-any.whl#sha256=758819cb5d94cdedf4e836988b74de396ceacb8e2794d21f82d131fd9ee77237
fastjsonschema==2.20.0
fastrlock @ file:///rapids/fastrlock-0.8.2-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_28_x86_64.whl#sha256=08315bde19d0c2e6b06593d5a418be3dc8f9b1ee721afa96867b9853fceb45cf
filelock==3.16.1
findpython==0.6.3
flash_attn==2.4.2
fonttools==4.54.1
fqdn==1.5.1
frozenlist @ file:///rapids/frozenlist-1.4.1-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=a9b2de4cf0cdd5bd2dee4c4f63a653c61d2408055ab77b151c1957f221cabf2a
fsspec @ file:///rapids/fsspec-2024.6.1-py3-none-any.whl#sha256=3cb443f8bcd2efb31295a5b9fdb02aee81d8452c80d28f97a6d0959e6cee101e
gast==0.6.0
gguf==0.16.3
googleapis-common-protos==1.70.0
grpcio @ file:///rapids/grpcio-1.62.1-cp310-cp310-linux_x86_64.whl#sha256=b573a4f1e2ce3b1a38ca9d33f421b1e73e0215810717405590810ad0bd6cdd79
h11==0.14.0
hf-xet==1.1.2
httpcore==1.0.6
httptools==0.6.4
httpx==0.27.2
huggingface-hub==0.31.2
hypothesis==5.35.1
idna @ file:///rapids/idna-3.7-py3-none-any.whl#sha256=82fee1fc78add43492d3a1898bfa6d8a904cc97d8427f683ed8e798d07761aa0
igraph==0.11.6
importlib_metadata @ file:///rapids/importlib_metadata-7.2.1-py3-none-any.whl#sha256=ffef94b0b66046dd8ea2d619b701fe978d9264d38f3998bc4c27ec3b146a87c8
iniconfig==2.0.0
installer==0.7.0
intel-openmp==2021.4.0
interegular==0.3.3
ipykernel==6.29.5
ipython==8.28.0
isoduration==20.11.0
isort==5.13.2
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jedi==0.19.1
jeepney==0.9.0
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
json5==0.9.25
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.2.5
jupyterlab-tensorboard-pro==4.0.0
jupyterlab_code_formatter==3.0.2
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupytext==1.16.4
keyring==25.6.0
kiwisolver==1.4.7
kvikio @ file:///rapids/kvikio-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=48475bb14764b1bb8c0d41d068474129b083b1a65d7fe526ed491b75b5456d60
langcodes==3.4.1
language_data==1.2.0
lark==1.2.2
lazy_loader==0.4
libkvikio @ file:///rapids/libkvikio-24.8.0-py3-none-linux_x86_64.whl#sha256=5ef1e43df60d9c8ad4beff3411a5ef8ffb84605d49dbf5e84434987fa1b74792
librmm @ file:///rapids/librmm-24.8.0-py3-none-linux_x86_64.whl#sha256=8e16f337d71dc3a953e99c0af406c034891484a2f903af9986ece38d82950c61
librosa==0.10.1
lightning-thunder==0.2.0.dev0
lightning-utilities==0.11.7
lintrunner==0.12.5
llguidance==0.7.20
llvmlite==0.44.0
lm-format-enforcer==0.10.11
locket @ file:///rapids/locket-1.0.0-py2.py3-none-any.whl#sha256=b6c819a722f7b6bd955b80781788e4a66a55628b858d347536b7e81325a3a5e3
looseversion==1.3.0
marisa-trie==1.2.0
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdit-py-plugins==0.4.2
mdurl==0.1.2
mistral_common==1.5.4
mistune==3.0.2
mkl==2021.1.1
mkl-devel==2021.1.1
mkl-include==2021.1.1
mock==5.1.0
modelscope==1.26.0
more-itertools==10.7.0
mpmath==1.3.0
msgpack @ file:///rapids/msgpack-1.0.8-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=00e073efcba9ea99db5acef3959efa45b52bc67b61b00823d2a1a6944bf45982
msgspec==0.19.0
multidict @ file:///rapids/multidict-6.0.5-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=21fd81c4ebdb4f214161be351eb5bcf385426bf023041da2fd9e60681f3cebae
multiprocess==0.70.16
murmurhash==1.0.10
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
ninja==********
notebook==7.2.2
notebook_shim==0.2.4
numba==0.61.2
numcodecs @ file:///rapids/numcodecs-0.11.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=c27dfca402f69fbfa01c46fb572086e77f38121192160cc8ed1177dc30702c52
numpy==1.26.4
nvfuser==0.2.10a0+f669fcf
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cudnn-frontend @ file:///opt/pytorch/lightning-thunder/tmp/cudnn_frontend
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-dali-cuda120==1.42.0
nvidia-modelopt==0.17.0
nvidia-nccl-cu12==2.21.5
nvidia-nvimgcodec-cu12==*******
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
nvidia-pyindex==1.0.9
nvtx @ file:///rapids/nvtx-0.2.5-cp310-cp310-linux_x86_64.whl#sha256=98d21d99771467bbb34219bfc553ebc830aaf0d25b7ae6c2b7c0e0a9c338ea9a
nx-cugraph @ file:///rapids/nx_cugraph-24.8.0-py3-none-any.whl#sha256=0e5de83bde7398493c415432446e521c7fa09a63ee2e04d372fb03bd0d23474c
onnx @ file:///opt/pytorch/pytorch/third_party/onnx
openai==1.79.0
opencv @ file:///opencv-4.7.0/modules/python/package
opencv-python-headless==4.11.0.86
opentelemetry-api==1.26.0
opentelemetry-exporter-otlp==1.26.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-exporter-otlp-proto-http==1.26.0
opentelemetry-proto==1.26.0
opentelemetry-sdk==1.26.0
opentelemetry-semantic-conventions==0.47b0
opentelemetry-semantic-conventions-ai==0.4.9
opt_einsum==3.4.0
optree==0.13.0
outlines==0.1.11
outlines_core==0.1.26
overrides==7.7.0
packaging==25.0
pandas @ file:///rapids/pandas-2.2.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=8635c16bf3d99040fdf3ca3db669a7250ddf49c55dc4aa8fe0ae0fa8d6dcc1f0
pandocfilters==1.5.1
parso==0.8.4
partd @ file:///rapids/partd-1.4.2-py3-none-any.whl#sha256=978e4ac767ec4ba5b86c6eaa52e5a2a3bc748a2ca839e8cc798f1cc6ce6efb0f
partial-json-parser==0.2.1.1.post5
pathspec==0.12.1
pbs-installer==2025.5.17
pexpect==4.9.0
pillow==10.4.0
pkginfo==1.12.1.2
platformdirs==4.3.6
pluggy==1.5.0
ply @ file:///rapids/ply-3.11-py2.py3-none-any.whl#sha256=096f9b8350b65ebd2fd1346b12452efe5b9607f7482813ffca50c22722a807ce
poetry==2.1.3
poetry-core==2.1.3
polars @ file:///rapids/polars-1.2.1-cp38-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=2094877da694bb59bfb7f5af50471955012b950068dc6f3ef5ecc41653a3b04f
polygraphy==0.49.12
pooch==1.8.2
preshed==3.0.9
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.21.0
prompt_toolkit==3.0.48
protobuf==4.24.4
psutil==6.0.0
ptyprocess==0.7.0
PuLP==2.9.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyarrow @ file:///rapids/pyarrow-16.1.0-cp310-cp310-linux_x86_64.whl#sha256=d1767bfd130aae44cca52b90f1b3db12b1fefe6303067cb37b50748bf429e58a
pybind11==2.13.6
pybind11_global==2.13.6
pycocotools @ git+https://github.com/nvidia/cocoapi.git@d99cbf3823588ef09a2721655f46e509ebafb3d7#subdirectory=PythonAPI
pycountry==24.6.1
pycparser==2.22
pydantic==2.9.2
pydantic_core==2.23.4
Pygments==2.18.0
pylibcugraph @ file:///rapids/pylibcugraph-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=5612b40cb76243138b9ba7e068a4389cb364fdda7dc2c9a25d058b2bcd6ae51a
pylibcugraphops @ file:///rapids/pylibcugraphops-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=6f11fd747528695932055016d7bce76590a36239e2c5acfe70a7f60e63ecd0bc
pylibraft @ file:///rapids/pylibraft-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=3a0470489c1272c1c7fe4d1f3f46777f0351dfee99cded2527b196819338d3f2
pylibwholegraph @ file:///rapids/pylibwholegraph-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=c17d84b7f314c3eb774ed9752d2e3237e1979e7b9c33036bde172a1cafb29164
pynvjitlink @ file:///rapids/pynvjitlink-0.3.0-cp310-cp310-linux_x86_64.whl#sha256=64953fb01011133f426c29c2c714cc673f9401132f3656b02db44ab9fc9ced2c
pynvml @ file:///rapids/pynvml-11.4.1-py3-none-any.whl#sha256=d27be542cd9d06558de18e2deffc8022ccd7355bc7382255d477038e7e424c6c
pyparsing==3.1.4
pyproject_hooks==1.2.0
pytest==8.1.1
pytest-flakefinder==1.1.0
pytest-rerunfailures==14.0
pytest-shard==0.1.2
pytest-xdist==3.6.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-hostlist==1.23.0
python-json-logger==2.0.7
python-multipart==0.0.20
pytorch-triton @ file:///tmp/dist/pytorch_triton-3.0.0%2Bdedb7bdf3-cp310-cp310-linux_x86_64.whl#sha256=d6f770e5e6b195feb8ba9c3ac057ced4b8faee549cbc07c94f4aaeca4443f6e6
pytz @ file:///rapids/pytz-2023.4-py2.py3-none-any.whl#sha256=f90ef520d95e7c46951105338d918664ebfd6f1d995bd7d153127ce90efafa6a
PyYAML==6.0.2
pyzmq==26.2.0
raft-dask @ file:///rapids/raft_dask-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=7ee8e86427e4a3b56da16d0f178da5cedb448afe79909eec6a8a00199623e379
RapidFuzz==3.13.0
rapids-dask-dependency @ file:///rapids/rapids_dask_dependency-24.8.0a0-py3-none-any.whl#sha256=1f9466a828f50baf066564a2417cfd5633ba2c5756eef4f15ff8dfd4e610671c
ray==2.46.0
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich @ file:///rapids/rich-13.7.1-py3-none-any.whl#sha256=4edbae314f59eb482f54e9e30bf00d33350aaa94f4bfcd4e9e3110e64d0d7222
rich-toolkit==0.14.6
rmm @ file:///rapids/rmm-24.8.0-cp310-cp310-linux_x86_64.whl#sha256=a9a2883b2a9855060c9cd6e15f5f5644d61cb5d5a814a0ee3d2e1ed42a967c94
rmm-cu12 @ file:///rapids/rmm_cu12-24.8.2-cp310-cp310-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl#sha256=6c6dc399dd24e4cc418280f1faca1d74b1a852e25e7798bf6bccf5e1d64f0c01
rpds-py==0.20.0
safetensors==0.4.5
scikit-learn==1.5.2
scipy @ file:///rapids/scipy-1.14.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=42470ea0195336df319741e230626b6225a740fd9dce9642ca13e98f667047c0
SecretStorage==3.3.3
Send2Trash==1.8.3
sentencepiece==0.2.0
shellingham==1.5.4
six==1.16.0
smart-open==7.0.4
sniffio==1.3.1
sortedcontainers==2.4.0
soundfile==0.12.1
soupsieve==2.6
soxr==0.5.0.post1
spacy==3.7.5
spacy-legacy==3.0.12
spacy-loggers==1.0.5
srsly==2.4.8
stack-data==0.6.3
starlette==0.46.2
sympy==1.13.1
tabulate==0.9.0
tbb==2021.13.1
tblib @ file:///rapids/tblib-3.0.0-py3-none-any.whl#sha256=80a6c77e59b55e83911e1e607c649836a69c103963c5f28a46cbeef44acf8129
tensorboard==2.16.2
tensorboard-data-server==0.7.2
tensorrt @ file:///workspace/TensorRT-*********/python/tensorrt-10.5.0-cp310-none-linux_x86_64.whl#sha256=f1312f6a09b4e4e99832f2f3f966446691af6985a2cd13548582318616cb6385
terminado==0.18.1
texttable==1.7.0
thinc==8.2.5
threadpoolctl==3.5.0
thriftpy2 @ file:///rapids/thriftpy2-0.4.20-cp310-cp310-linux_x86_64.whl#sha256=ca20dc6a00fbe3697c7047a55aace12388abe90106634258d24eaff12bdf3ca5
tiktoken==0.9.0
tinycss2==1.3.0
tokenizers==0.21.1
tomli==2.0.2
tomlkit==0.13.2
toolz @ file:///rapids/toolz-0.12.1-py3-none-any.whl#sha256=d22731364c07d72eea0a0ad45bafb2c2937ab6fd38a3507bf55eae8744aa7d85
torch==2.6.0
torch_tensorrt @ file:///opt/pytorch/torch_tensorrt/dist/torch_tensorrt-2.5.0a0-cp310-cp310-linux_x86_64.whl#sha256=81963de8e48c073b81c9f6db8ba61c003e2d43b86a51e0f2f6114d60d9a39145
torchaudio==2.6.0
torchprofile==0.0.4
torchvision==0.21.0
tornado==6.2
tqdm==4.66.5
traitlets==5.14.3
transformer_engine @ git+https://github.com/NVIDIA/TransformerEngine.git@4df84889cb5743113b0fa59839b941486df16ace
transformers==4.51.3
treelite @ file:///rapids/treelite-4.3.0-py3-none-linux_x86_64.whl#sha256=ba55ba43ab4a0eca04dd9cfd0c114bae0f88a96b6768fb204858fe6f979c961f
triton==3.2.0
trove-classifiers==2025.5.9.12
typer==0.12.5
types-dataclasses==0.6.6
types-python-dateutil==2.9.0.20241003
typing_extensions==4.12.2
tzdata @ file:///rapids/tzdata-2024.1-py2.py3-none-any.whl#sha256=9068bc196136463f5245e51efda838afa15aaeca9903f49050dfa2679db4d252
ucx-py @ file:///rapids/ucx_py-0.39.0-cp310-cp310-linux_x86_64.whl#sha256=2af491edff0cc326fa99e5c6e42505ae1a10f25ced1f767317772a8db3a661f1
ucxx @ file:///rapids/ucxx-0.39.0-cp310-cp310-linux_x86_64.whl#sha256=e9994bfc9b96605299065db917da04c8ff5d3cc8ea8be15756f510d2ae33ad61
uri-template==1.3.0
urllib3 @ file:///rapids/urllib3-2.0.7-py3-none-any.whl#sha256=fdb6d215c776278489906c2f8916e6e7d4f5a9b602ccbcfdf7f016fc8da0596e
uvicorn==0.34.2
uvloop==0.21.0
virtualenv==20.31.2
vllm==0.8.5
wasabi==1.1.3
watchfiles==1.0.5
wcwidth==0.2.13
weasel==0.4.1
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.0.4
wrapt==1.16.0
xdoctest==1.0.2
xformers==0.0.29.post2
xgboost @ file:///rapids/xgboost-2.1.1-py3-none-linux_x86_64.whl#sha256=0e6bb72d69b26d0e31ffc581fa6155301760a331efdbc03dc6fc9ed4bcc44a1f
xgrammar==0.1.18
xxhash==3.5.0
yarl @ file:///rapids/yarl-1.9.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl#sha256=357495293086c5b6d34ca9616a43d329317feab7917518bc97a08f9e55648455
zarr @ file:///rapids/zarr-2.18.2-py3-none-any.whl#sha256=a638754902f97efa99b406083fdc807a0e2ccf12a949117389d2a4ba9b05df38
zict @ file:///rapids/zict-3.0.0-py2.py3-none-any.whl#sha256=5796e36bd0e0cc8cf0fbc1ace6a68912611c1dbd74750a3f3026b9b9d6a327ae
zipp @ file:///rapids/zipp-3.20.0-py3-none-any.whl#sha256=58da6168be89f0be59beb194da1250516fdaa062ccebd30127ac65d30045e10d
zstandard==0.23.0
