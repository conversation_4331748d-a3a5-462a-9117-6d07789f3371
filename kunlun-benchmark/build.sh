#!/bin/bash
pip install poetry -i https://pypi.tuna.tsinghua.edu.cn/simple

# 获取当前环境中的包列表
pip freeze > current_packages.txt

# 要安装的包列表
packages=("requests" "prettytable" "pytz" "transformers" "pydantic" "datasets" "click" "loguru" "oss2" "jsonlines")

# 遍历每个包并检查是否存在
for pkg in "${packages[@]}"; do
    if grep -q "$pkg" current_packages.txt; then
        echo "Package '$pkg' already exists."
    else
        echo "Installing package '$pkg'."
        poetry add "$pkg"
    fi
done

# 最终安装所有包
poetry install --no-root
