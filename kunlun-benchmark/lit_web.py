import os
import time
import typing

import click
import litserve as ls
import torch
from diffusers import AutoPipelineForText2Image
from pydantic import BaseModel, Field


class ReqBody(BaseModel):
    prompt: typing.Union[str, typing.List[str]] = Field(..., description="prompt")
    # negative_prompt: typing.Optional[str] = Field("", description="negative prompt")
    num_inference_steps: int = Field(20, ge=5, description="inference steps")
    width: int = Field(512, description="image width")
    height: int = Field(512, description="image height")
    # 引导尺度，控制文本提示对生成图像的影响程度。较高的值会使生成的图像更加符合文本提示
    guidance_scale: float = Field(7.0, description="guidance scale")
    num_images_per_prompt: int = Field(1, description="num images per prompt")
    seek: int = Field(1, description="seek")

    def to_dashark(self):
        return {
            "prompt": self.prompt,
            "txt2img_args": {
                "width": self.width,
                "height": self.height,
            },
            "num_inference_steps": self.num_inference_steps,
            "guidance_scale": self.guidance_scale,
            "num_images_per_prompt": self.num_images_per_prompt,
            "seek": self.seek,
        }


class Resp(BaseModel):
    cost_time: float = Field(..., description="generate cost time(ms)")
    results: list[str] = Field(description="images", default_factory=list)


DEVICE = "cuda" if torch.cuda.is_available() else "cpu"


tensor_map = {
    "stable-diffusion-xl-base-1.0_diffusers_FP16_default_no-split": "sd_xl_base_1.0.safetensors",
    "FLUX.1-dev_diffusers_FP16_default_no-split": "flux1-dev.safetensors",
    "FLUX.1-schnell_diffusers_FP16_default_no-split": "flux1-schnell.safetensors",
}


def model_path_to_safetensors(model_path):
    p = model_path
    if p.endswith(os.sep):
        p = p.rstrip(os.sep)

    folder_name = os.path.basename(p)
    if folder_name not in tensor_map:
        raise ValueError(f"model {model_path} not support, please use model safetensors file")
    tensor_file = tensor_map[folder_name]
    return os.path.join(model_path, tensor_file)


class SimpleLitAPI(ls.LitAPI):

    def __init__(self, args):
        super().__init__()
        self.args = args
        self.framework = args["framework"]
        self.model = None
        self.generator = None
        self._dashark = None

    @property
    def dashark(self):
        if self._dashark is None:
            self._dashark = __import__("dashark_sdk")
        return self._dashark

    def setup(self, device):
        if self.framework == "diffusers":
            return self.setup_diffusers()
        elif self.framework == "dashark":
            return self.setup_dashark()
        raise NotImplementedError(f"framework {self.framework} not support")

    def setup_diffusers(self):
        self.model = AutoPipelineForText2Image.from_pretrained(
            self.args["model_path"], torch_dtype=self.args["torch_dtype"]
        ).to(DEVICE)
        self.generator = torch.Generator(DEVICE).manual_seed(24)

        prompt = "A lots of girls dancing"
        _ = self.model(prompt, num_inference_steps=2).images[0]

    def setup_dashark(self):
        model_path = self.args["model_path"]
        if not model_path.endswith(".safetensors"):
            model_path = model_path_to_safetensors(model_path)

        self.model = self.dashark.DynamicStableDiffusion(device="cuda:0")
        self.model.update(self.dashark.DynamicPipelineArgs(model_path=model_path))
        args = self.dashark.StableDiffusionArgs(
            prompt="A lots of girls dancing", txt2img_args=self.dashark.Txt2ImgArgs(), num_inference_steps=5, seed=1
        )
        _ = self.model(args)[0][0]

    def decode_request(self, request: ReqBody, **kwargs):
        return super().decode_request(request, **kwargs)

    def predict(self, x: ReqBody, **kwargs):
        if self.framework == "diffusers":
            return self.predict_diffusers(x)
        elif self.framework == "dashark":
            return self.predict_dashark(x)
        raise NotImplementedError(f"framework {self.framework} not support")

    def predict_diffusers(self, x: ReqBody, **kwargs):
        start = time.perf_counter()
        ims = self.model(generator=self.generator, **x.model_dump(exclude={"seek"}))
        cost = time.perf_counter() - start
        resp = Resp(cost_time=cost * 1000)
        for _ in ims.images:
            resp.results.append("")
        return resp

    def predict_dashark(self, x: ReqBody, **kwargs):
        start = time.perf_counter()
        ims = self.model(self.dashark.StableDiffusionArgs(seed=24, **x.to_dashark()))
        cost = time.perf_counter() - start
        resp = Resp(cost_time=cost * 1000)
        for _ in ims[0]:
            resp.results.append("")
        return resp

    def encode_response(self, output, **kwargs) -> Resp:
        # Convert the model output to a response payload.
        return output


@click.command()
@click.option("--framework",  type=click.Choice(["diffusers", "dashark"], case_sensitive=False), help="text2image framework", default="diffusers")  # noqa
@click.option("--model-path", type=str, help="model path")
@click.option("--dtype", type=str, help="torch dtype", default="float16")
@click.option("--port", type=int, help="run port", default=30001)
@click.option("-mbs", "--max-batch-size", type=int, help="max batch size", default=1)
def run_server(framework, model_path, dtype, port, max_batch_size):
    try:
        torch_dtype = getattr(torch, dtype)
    except AttributeError:
        click.echo(f"{dtype} is not a valid torch dtype")
        return
    args = {"model_path": model_path, "torch_dtype": torch_dtype, "framework": framework}
    server = ls.LitServer(SimpleLitAPI(args=args), max_batch_size=max_batch_size)
    server.run(port=port)


if __name__ == "__main__":
    run_server()
